import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { appSettings } from '@/constants/Data';
import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { Alert, ScrollView, StyleSheet, View } from 'react-native';
import { Button, Card, Divider, List } from 'react-native-paper';

export default function SettingsScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: () => console.log('Logout pressed') },
      ]
    );
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>Settings</ThemedText>
      </ThemedView>

      {/* Settings Card */}
      <Card style={[styles.settingsCard, { backgroundColor: colors.surface }]} elevation={2}>
        <Card.Content>
          <List.Section>
            <List.Item
              title="Theme"
              description={appSettings.theme}
              left={(props) => <List.Icon {...props} icon="palette" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => console.log('Theme settings pressed')}
            />
            <Divider />
            <List.Item
              title="Notifications"
              description="Manage notification preferences"
              left={(props) => <List.Icon {...props} icon="bell" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => console.log('Notifications pressed')}
            />
            <Divider />
            <List.Item
              title="Privacy"
              description="Privacy and security settings"
              left={(props) => <List.Icon {...props} icon="shield-check" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => console.log('Privacy pressed')}
            />
            <Divider />
            <List.Item
              title="App Version"
              description={appSettings.version}
              left={(props) => <List.Icon {...props} icon="information" />}
            />
          </List.Section>
        </Card.Content>
      </Card>

      {/* About Section */}
      <Card style={[styles.aboutCard, { backgroundColor: colors.surface }]} elevation={2}>
        <Card.Content>
          <ThemedText style={styles.aboutTitle}>About Agent Manager</ThemedText>
          <ThemedText style={styles.aboutText}>
            Agent Manager is a simple React Native app built with Expo and React Navigation.
            It demonstrates modern mobile app development practices with a clean, intuitive interface.
          </ThemedText>
        </Card.Content>
      </Card>

      {/* Logout Button */}
      <View style={styles.logoutContainer}>
        <Button
          mode="contained"
          onPress={handleLogout}
          style={[styles.logoutButton, { backgroundColor: colors.error }]}
          contentStyle={styles.logoutButtonContent}
          labelStyle={styles.logoutButtonLabel}
        >
          Logout
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  settingsCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 12,
  },
  aboutCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 12,
  },
  aboutTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  aboutText: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.7,
  },
  logoutContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  logoutButton: {
    borderRadius: 8,
  },
  logoutButtonContent: {
    paddingVertical: 8,
  },
  logoutButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
});
