import React, { useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Card, List, Switch, Divider } from 'react-native-paper';
import { Stack } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  icon: string;
  enabled: boolean;
}

export default function NotificationsScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const [notifications, setNotifications] = useState<NotificationSetting[]>([
    {
      id: 'push',
      title: 'Push Notifications',
      description: 'Receive notifications on your device',
      icon: 'bell',
      enabled: true,
    },
    {
      id: 'tasks',
      title: 'Task Reminders',
      description: 'Get reminded about upcoming tasks',
      icon: 'clipboard-check',
      enabled: true,
    },
    {
      id: 'meetings',
      title: 'Meeting Alerts',
      description: 'Notifications for scheduled meetings',
      icon: 'calendar-clock',
      enabled: true,
    },
    {
      id: 'email',
      title: 'Email Notifications',
      description: 'Receive email updates',
      icon: 'email',
      enabled: false,
    },
    {
      id: 'sound',
      title: 'Sound',
      description: 'Play sound with notifications',
      icon: 'volume-high',
      enabled: true,
    },
    {
      id: 'vibration',
      title: 'Vibration',
      description: 'Vibrate device for notifications',
      icon: 'vibrate',
      enabled: true,
    },
  ]);

  const toggleNotification = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, enabled: !notification.enabled }
          : notification
      )
    );
  };

  const generalNotifications = notifications.slice(0, 4);
  const soundAndVibration = notifications.slice(4);

  return (
    <>
      <Stack.Screen 
        options={{ 
          title: 'Notifications',
          headerShown: true,
          headerStyle: { backgroundColor: colors.surface },
          headerTintColor: colors.text,
        }} 
      />
      <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedView style={styles.header}>
          <ThemedText type="title" style={styles.headerTitle}>Notification Settings</ThemedText>
          <ThemedText style={styles.headerSubtitle}>
            Manage how you receive notifications from Agent Manager
          </ThemedText>
        </ThemedView>

        {/* General Notifications */}
        <Card style={[styles.card, { backgroundColor: colors.surface }]} elevation={2}>
          <Card.Content>
            <ThemedText style={styles.sectionTitle}>General</ThemedText>
            
            {generalNotifications.map((notification, index) => (
              <View key={notification.id}>
                <List.Item
                  title={notification.title}
                  description={notification.description}
                  left={(props) => <List.Icon {...props} icon={notification.icon} />}
                  right={() => (
                    <Switch
                      value={notification.enabled}
                      onValueChange={() => toggleNotification(notification.id)}
                      color={colors.primary}
                    />
                  )}
                  onPress={() => toggleNotification(notification.id)}
                />
                {index < generalNotifications.length - 1 && <Divider />}
              </View>
            ))}
          </Card.Content>
        </Card>

        {/* Sound & Vibration */}
        <Card style={[styles.card, { backgroundColor: colors.surface }]} elevation={2}>
          <Card.Content>
            <ThemedText style={styles.sectionTitle}>Sound & Vibration</ThemedText>
            
            {soundAndVibration.map((notification, index) => (
              <View key={notification.id}>
                <List.Item
                  title={notification.title}
                  description={notification.description}
                  left={(props) => <List.Icon {...props} icon={notification.icon} />}
                  right={() => (
                    <Switch
                      value={notification.enabled}
                      onValueChange={() => toggleNotification(notification.id)}
                      color={colors.primary}
                    />
                  )}
                  onPress={() => toggleNotification(notification.id)}
                />
                {index < soundAndVibration.length - 1 && <Divider />}
              </View>
            ))}
          </Card.Content>
        </Card>

        {/* Info Card */}
        <Card style={[styles.infoCard, { backgroundColor: colors.surface }]} elevation={1}>
          <Card.Content>
            <ThemedText style={styles.infoTitle}>Notification Tips</ThemedText>
            <ThemedText style={styles.infoText}>
              • Turn off notifications during focus time to minimize distractions
            </ThemedText>
            <ThemedText style={styles.infoText}>
              • Task reminders help you stay on top of your schedule
            </ThemedText>
            <ThemedText style={styles.infoText}>
              • Email notifications can be managed separately from push notifications
            </ThemedText>
          </Card.Content>
        </Card>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    opacity: 0.7,
    lineHeight: 22,
  },
  card: {
    margin: 20,
    marginTop: 10,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  infoCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
    opacity: 0.8,
  },
});
