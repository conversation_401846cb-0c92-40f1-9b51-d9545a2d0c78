import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { agentCapabilities, AgentCapability, agentProfile, SummaryCard, summaryCards } from '@/constants/Data';
import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { FlatList, ScrollView, StyleSheet, View } from 'react-native';
import { Avatar, Card, Chip } from 'react-native-paper';

export default function DashboardScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const renderSummaryCard = ({ item }: { item: SummaryCard }) => (
    <Card style={[styles.summaryCard, { backgroundColor: colors.surface }]} elevation={2}>
      <Card.Content style={styles.summaryCardContent}>
        <View style={styles.summaryCardHeader}>
          <ThemedText style={styles.summaryIcon}>{item.icon}</ThemedText>
          <ThemedText style={[styles.summaryValue, { color: item.color }]}>{item.value}</ThemedText>
        </View>
        <ThemedText style={styles.summaryTitle}>{item.title}</ThemedText>
      </Card.Content>
    </Card>
  );

  const renderCapabilityCard = ({ item }: { item: AgentCapability }) => (
    <Card style={[styles.capabilityCard, { backgroundColor: colors.surface }]} elevation={1}>
      <Card.Content style={styles.capabilityContent}>
        <View style={styles.capabilityHeader}>
          <ThemedText style={styles.capabilityIcon}>{item.icon}</ThemedText>
          <View style={styles.capabilityText}>
            <ThemedText style={styles.capabilityTitle}>{item.title}</ThemedText>
            <ThemedText style={styles.capabilityDescription}>{item.description}</ThemedText>
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <ThemedText type="title" style={styles.headerTitle}>Agent Manager</ThemedText>
        <ThemedText style={styles.greeting}>Hello, Admin</ThemedText>
      </ThemedView>

      {/* Agent Profile Card */}
      <Card style={[styles.profileCard, { backgroundColor: colors.surface }]} elevation={3}>
        <Card.Content style={styles.profileContent}>
          <View style={styles.profileHeader}>
            <Avatar.Text size={60} label="🤖" style={styles.avatar} />
            <View style={styles.profileInfo}>
              <ThemedText style={styles.agentName}>{agentProfile.name}</ThemedText>
              <ThemedText style={styles.agentRole}>{agentProfile.role}</ThemedText>
              <Chip
                mode="outlined"
                style={[styles.statusChip, { backgroundColor: colors.success }]}
                textStyle={{ color: 'white', fontSize: 12 }}
              >
                {agentProfile.status}
              </Chip>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Summary Cards */}
      <ThemedView style={styles.section}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>Overview</ThemedText>
        <FlatList
          data={summaryCards}
          renderItem={renderSummaryCard}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.summaryContainer}
        />
      </ThemedView>

      {/* Agent Capabilities */}
      <ThemedView style={styles.section}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>Agent Capabilities</ThemedText>
        <FlatList
          data={agentCapabilities}
          renderItem={renderCapabilityCard}
          keyExtractor={(item) => item.id}
          scrollEnabled={false}
          contentContainerStyle={styles.capabilitiesContainer}
        />
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  greeting: {
    fontSize: 16,
    opacity: 0.7,
  },
  profileCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 12,
  },
  profileContent: {
    padding: 20,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  agentName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  agentRole: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 8,
  },
  statusChip: {
    alignSelf: 'flex-start',
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 12,
  },
  summaryContainer: {
    paddingRight: 20,
  },
  summaryCard: {
    width: 140,
    marginRight: 12,
    borderRadius: 12,
  },
  summaryCardContent: {
    padding: 16,
  },
  summaryCardHeader: {
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  summaryTitle: {
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.7,
  },
  capabilitiesContainer: {
    gap: 12,
  },
  capabilityCard: {
    borderRadius: 12,
  },
  capabilityContent: {
    padding: 16,
  },
  capabilityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  capabilityIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  capabilityText: {
    flex: 1,
  },
  capabilityTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  capabilityDescription: {
    fontSize: 14,
    opacity: 0.7,
  },
});
