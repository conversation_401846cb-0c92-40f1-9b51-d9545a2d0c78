export interface AgentCapability {
  id: string;
  title: string;
  description: string;
  icon: string;
}

export interface SummaryCard {
  id: string;
  title: string;
  value: string;
  icon: string;
  color: string;
}

export interface AgentProfile {
  name: string;
  role: string;
  status: 'Online' | 'Offline' | 'Busy';
  avatar: string;
}

export const agentProfile: AgentProfile = {
  name: 'Ava - AI Agent',
  role: 'Scheduler & Assistant',
  status: 'Online',
  avatar: '🤖',
};

export const summaryCards: SummaryCard[] = [
  {
    id: '1',
    title: 'Tasks',
    value: '12 pending',
    icon: '📋',
    color: '#6366f1',
  },
  {
    id: '2',
    title: 'Meetings',
    value: '3 today',
    icon: '📅',
    color: '#10b981',
  },
  {
    id: '3',
    title: 'Alerts',
    value: '5 unread',
    icon: '🔔',
    color: '#f59e0b',
  },
];

export const agentCapabilities: AgentCapability[] = [
  {
    id: '1',
    title: 'Voice Commands',
    description: 'Process and respond to voice instructions',
    icon: '🎤',
  },
  {
    id: '2',
    title: 'Task Automation',
    description: 'Automate repetitive tasks and workflows',
    icon: '⚙️',
  },
  {
    id: '3',
    title: 'Data Lookup',
    description: 'Search and retrieve information quickly',
    icon: '🔍',
  },
  {
    id: '4',
    title: 'Notification Alerts',
    description: 'Send timely notifications and reminders',
    icon: '🔔',
  },
  {
    id: '5',
    title: 'Schedule Management',
    description: 'Manage calendars and appointments',
    icon: '📅',
  },
];

export const appSettings = {
  theme: 'Light',
  version: '1.0.0',
};
