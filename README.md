# Agent Manager 🤖

A simple React Native management app built with Expo and React Navigation. This is a layout-focused beginner project showcasing modern mobile app development practices with a clean, intuitive interface.

## Features

### 📱 Dashboard Screen
- **Header**: "Agent Manager" title with "Hello, Admin" greeting
- **Profile Card**: AI Agent "Ava" with avatar, status (Online), and role (Scheduler & Assistant)
- **Summary Cards**: Grid-style overview showing:
  - 📋 Tasks: 12 pending
  - 📅 Meetings: 3 today
  - 🔔 Alerts: 5 unread
- **Agent Capabilities**: Scrollable list of AI agent features:
  - Voice Commands
  - Task Automation
  - Data Lookup
  - Notification Alerts
  - Schedule Management

### ⚙️ Settings Screen
- **Settings Options**: Theme, Notifications, Privacy
- **App Information**: Version 1.0.0
- **About Section**: App description
- **Logout Button**: Non-functional demo button

## Tech Stack

- **React Native** with Expo
- **React Navigation** (Bottom Tabs)
- **React Native Paper** for UI components
- **TypeScript** for type safety
- **Expo Router** for file-based routing

## Getting Started

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start the development server**
   ```bash
   npm start
   ```

3. **Run on different platforms**
   - Press `w` for web
   - Press `a` for Android emulator
   - Press `i` for iOS simulator
   - Scan QR code with Expo Go app

## Project Structure

```
app/
├── (tabs)/
│   ├── index.tsx          # Dashboard screen
│   ├── settings.tsx       # Settings screen
│   └── _layout.tsx        # Tab navigation layout
├── _layout.tsx            # Root layout with providers
constants/
├── Colors.ts              # Theme colors
└── Data.ts               # Static sample data
```

## Key Components

- **Dashboard**: Clean layout with profile card, summary cards, and capabilities list
- **Settings**: Standard settings interface with list items and logout functionality
- **Navigation**: Bottom tab navigation between Home and Settings
- **Theming**: Light/dark mode support with React Native Paper

## Sample Data

The app uses hardcoded sample data (no backend required):
- Agent profile information
- Summary statistics
- Capability descriptions
- Settings options

## Development Notes

- Uses functional components with React hooks
- Implements proper TypeScript interfaces
- Follows React Native best practices
- Responsive design with proper spacing and shadows
- Clean, modern UI with Material Design components

## Perfect for Learning

This project is ideal for beginners to practice:
- React Native component structure
- Navigation setup and configuration
- UI library integration (React Native Paper)
- TypeScript in React Native
- Layout design and styling
- State management basics

## License

This project is for educational purposes.
