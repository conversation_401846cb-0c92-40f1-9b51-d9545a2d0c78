import React, { useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Card, List, RadioButton, Divider } from 'react-native-paper';
import { Stack, router } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

type ThemeOption = 'light' | 'dark' | 'system';

export default function ThemeScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [selectedTheme, setSelectedTheme] = useState<ThemeOption>('system');

  const themeOptions = [
    {
      value: 'light' as ThemeOption,
      title: 'Light',
      description: 'Use light theme',
      icon: 'white-balance-sunny',
    },
    {
      value: 'dark' as ThemeOption,
      title: 'Dark',
      description: 'Use dark theme',
      icon: 'moon-waning-crescent',
    },
    {
      value: 'system' as ThemeOption,
      title: 'System',
      description: 'Follow system setting',
      icon: 'cog',
    },
  ];

  const handleThemeChange = (theme: ThemeOption) => {
    setSelectedTheme(theme);
    // In a real app, you would save this preference and apply it
    console.log('Theme changed to:', theme);
  };

  return (
    <>
      <Stack.Screen 
        options={{ 
          title: 'Theme Settings',
          headerShown: true,
          headerStyle: { backgroundColor: colors.surface },
          headerTintColor: colors.text,
        }} 
      />
      <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedView style={styles.header}>
          <ThemedText type="title" style={styles.headerTitle}>Theme Settings</ThemedText>
          <ThemedText style={styles.headerSubtitle}>
            Choose your preferred theme for the app
          </ThemedText>
        </ThemedView>

        <Card style={[styles.card, { backgroundColor: colors.surface }]} elevation={2}>
          <Card.Content>
            <ThemedText style={styles.sectionTitle}>Appearance</ThemedText>
            
            {themeOptions.map((option, index) => (
              <View key={option.value}>
                <List.Item
                  title={option.title}
                  description={option.description}
                  left={(props) => <List.Icon {...props} icon={option.icon} />}
                  right={() => (
                    <RadioButton
                      value={option.value}
                      status={selectedTheme === option.value ? 'checked' : 'unchecked'}
                      onPress={() => handleThemeChange(option.value)}
                      color={colors.primary}
                    />
                  )}
                  onPress={() => handleThemeChange(option.value)}
                />
                {index < themeOptions.length - 1 && <Divider />}
              </View>
            ))}
          </Card.Content>
        </Card>

        <Card style={[styles.infoCard, { backgroundColor: colors.surface }]} elevation={1}>
          <Card.Content>
            <ThemedText style={styles.infoTitle}>About Themes</ThemedText>
            <ThemedText style={styles.infoText}>
              • <ThemedText style={styles.bold}>Light:</ThemedText> Always use light theme
            </ThemedText>
            <ThemedText style={styles.infoText}>
              • <ThemedText style={styles.bold}>Dark:</ThemedText> Always use dark theme
            </ThemedText>
            <ThemedText style={styles.infoText}>
              • <ThemedText style={styles.bold}>System:</ThemedText> Automatically switch based on your device settings
            </ThemedText>
          </Card.Content>
        </Card>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    opacity: 0.7,
    lineHeight: 22,
  },
  card: {
    margin: 20,
    marginTop: 10,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  infoCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
    opacity: 0.8,
  },
  bold: {
    fontWeight: '600',
  },
});
